import React from 'react';
import { View, StyleSheet } from 'react-native';
import { InputToolbar, InputToolbarProps, IMessage } from 'react-native-gifted-chat';
import { useTheme } from '@/src/context/ThemeContext';
import ReplyPreview from './ReplyPreview';

interface CustomInputToolbarProps extends InputToolbarProps<IMessage> {
  replyToMessage?: any;
  onCancelReply?: () => void;
  currentUserId: string;
}

const GiftedChatInputToolbar: React.FC<CustomInputToolbarProps> = ({
  replyToMessage,
  onCancelReply,
  currentUserId,
  ...props
}) => {
  const { colors, isDarkMode } = useTheme();

  return (
    <View style={styles.container}>
      {/* Reply Preview */}
      {replyToMessage && onCancelReply && (
        <ReplyPreview
          replyToMessage={replyToMessage}
          currentUserId={currentUserId}
          onCancel={onCancelReply}
        />
      )}
      
      {/* Input Toolbar */}
      <InputToolbar
        {...props}
        containerStyle={[
          styles.inputToolbar,
          {
            backgroundColor: "#000000", // Solid black background as preferred
            borderTopColor: isDarkMode 
              ? 'rgba(128, 128, 128, 0.2)' 
              : 'rgba(128, 128, 128, 0.2)',
          }
        ]}
        primaryStyle={[
          styles.primaryStyle,
          {
            backgroundColor: colors.input,
            borderColor: colors.inputBorder,
          }
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#000000", // Solid black background
  },
  inputToolbar: {
    borderTopWidth: 1,
    paddingHorizontal: 8,
    paddingVertical: 8,
    minHeight: 60,
  },
  primaryStyle: {
    alignItems: 'center',
    borderRadius: 25,
    borderWidth: 1,
    marginHorizontal: 8,
    paddingHorizontal: 12,
  },
});

export default GiftedChatInputToolbar;
