import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { IMessage } from 'react-native-gifted-chat';
import { useTheme } from '@/src/context/ThemeContext';
import { useDispatch } from 'react-redux';
import { selectMessageForReaction } from '@/src/store/slices/chatUISlice';
import * as Haptics from 'expo-haptics';

interface GiftedChatMessageWrapperProps {
  children: React.ReactNode;
  message: IMessage;
  currentUserId: string;
  onSwipeReply?: (message: IMessage) => void;
  onLongPress?: (message: IMessage) => void;
}

const GiftedChatMessageWrapper: React.FC<GiftedChatMessageWrapperProps> = ({
  children,
  message,
  currentUserId,
  onSwipeReply,
  onLongPress,
}) => {
  const { colors } = useTheme();
  const dispatch = useDispatch();

  const isOwnMessage = message.user._id.toString() === currentUserId;

  // Simplified handlers without gesture animations
  const handleLongPress = () => {
    // Trigger haptic feedback
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    // Dispatch Redux action for message selection
    dispatch(selectMessageForReaction({
      messageId: message._id.toString(),
      position: { x: 0, y: 0, width: 0, height: 0 } // Simplified for now
    }));

    // Call legacy callback if provided
    if (onLongPress) {
      onLongPress(message);
    }
  };

  // Temporarily disable swipe functionality - just render with long press
  return (
    <TouchableOpacity
      onLongPress={handleLongPress}
      delayLongPress={500}
      activeOpacity={1}
      style={styles.container}
    >
      {children}
    </TouchableOpacity>
  );
};

export default GiftedChatMessageWrapper;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
