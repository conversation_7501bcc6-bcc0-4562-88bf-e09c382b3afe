import { IMessage, User } from 'react-native-gifted-chat';

// Current app message interface
export interface AppMessage {
  id: string;
  content: string;
  sender_id: string;
  receiver_id: string;
  created_at: string;
  is_read: boolean;
  status: "sent" | "delivered" | "read";
  delivered_at?: string;
  read_at?: string;
  message_type?: "text" | "shared_post" | "shared_reel";
  reply_to_message_id?: string;
  reply_to_message?: {
    id: string;
    content: string;
    sender_id: string;
    message_type?: string;
  };
  reactions?: Array<{
    id: string;
    message_id: string;
    user_id: string;
    emoji: string;
    created_at: string;
  }>;
}

// User profile interface for GiftedChat
export interface AppUser {
  id: string;
  username: string;
  avatar_url?: string;
  name?: string;
}

/**
 * Convert app message to GiftedChat message format
 */
export const convertToGiftedChatMessage = (
  appMessage: AppMessage,
  currentUserId: string,
  userProfiles: Record<string, AppUser> = {}
): IMessage => {
  const sender = userProfiles[appMessage.sender_id];
  
  // Create GiftedChat user object
  const user: User = {
    _id: appMessage.sender_id,
    name: sender?.username || sender?.name || 'Unknown User',
    avatar: sender?.avatar_url,
  };

  // Base GiftedChat message
  const giftedMessage: IMessage = {
    _id: appMessage.id,
    text: appMessage.content,
    createdAt: new Date(appMessage.created_at),
    user,
    sent: appMessage.status === 'sent' || appMessage.status === 'delivered' || appMessage.status === 'read',
    received: appMessage.status === 'delivered' || appMessage.status === 'read',
    pending: false, // We'll handle this separately for optimistic updates
  };

  // Add custom properties for our app-specific features
  (giftedMessage as any).appMessage = appMessage; // Store original message for reference
  (giftedMessage as any).messageType = appMessage.message_type || 'text';
  (giftedMessage as any).replyToMessage = appMessage.reply_to_message;
  (giftedMessage as any).reactions = appMessage.reactions || [];
  (giftedMessage as any).isRead = appMessage.is_read;

  // Handle different message types
  if (appMessage.message_type === 'shared_post') {
    try {
      const postData = JSON.parse(appMessage.content);
      giftedMessage.text = `📷 Shared a post by @${postData.post_owner}`;
      (giftedMessage as any).sharedPost = postData;
    } catch (error) {
      giftedMessage.text = '📷 Shared a post';
    }
  } else if (appMessage.message_type === 'shared_reel') {
    try {
      const reelData = JSON.parse(appMessage.content);
      giftedMessage.text = `🎥 Shared a reel by @${reelData.reel_owner}`;
      (giftedMessage as any).sharedReel = reelData;
    } catch (error) {
      giftedMessage.text = '🎥 Shared a reel';
    }
  }

  return giftedMessage;
};

/**
 * Convert GiftedChat message to app message format
 */
export const convertFromGiftedChatMessage = (
  giftedMessage: IMessage,
  receiverId: string,
  messageType: "text" | "shared_post" | "shared_reel" = "text"
): Omit<AppMessage, 'id' | 'created_at'> => {
  return {
    content: giftedMessage.text,
    sender_id: giftedMessage.user._id.toString(),
    receiver_id: receiverId,
    is_read: false,
    status: "sent",
    message_type: messageType,
  };
};

/**
 * Convert array of app messages to GiftedChat messages
 */
export const convertMessagesToGiftedChat = (
  appMessages: AppMessage[],
  currentUserId: string,
  userProfiles: Record<string, AppUser> = {}
): IMessage[] => {
  return appMessages
    .map(message => convertToGiftedChatMessage(message, currentUserId, userProfiles))
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()); // GiftedChat expects newest first
};

/**
 * Create optimistic message for immediate UI update
 */
export const createOptimisticMessage = (
  text: string,
  senderId: string,
  receiverId: string,
  userProfiles: Record<string, AppUser> = {},
  messageType: "text" | "shared_post" | "shared_reel" = "text"
): IMessage => {
  const tempId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  const sender = userProfiles[senderId];

  const user: User = {
    _id: senderId,
    name: sender?.username || sender?.name || 'You',
    avatar: sender?.avatar_url,
  };

  return {
    _id: tempId,
    text,
    createdAt: new Date(),
    user,
    pending: true, // Mark as pending for optimistic update
    sent: false,
    received: false,
  };
};

/**
 * Create optimistic reply message for immediate UI update
 */
export const createOptimisticReplyMessage = (
  text: string,
  senderId: string,
  receiverId: string,
  replyToMessage: AppMessage,
  userProfiles: Record<string, AppUser> = {}
): IMessage => {
  const tempId = `temp_reply_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  const sender = userProfiles[senderId];

  const user: User = {
    _id: senderId,
    name: sender?.username || sender?.name || 'You',
    avatar: sender?.avatar_url,
  };

  const giftedMessage: IMessage = {
    _id: tempId,
    text,
    createdAt: new Date(),
    user,
    pending: true,
    sent: false,
    received: false,
  };

  // Add reply information
  (giftedMessage as any).replyToMessage = {
    id: replyToMessage.id,
    content: replyToMessage.content,
    sender_id: replyToMessage.sender_id,
    message_type: replyToMessage.message_type,
  };

  return giftedMessage;
};

/**
 * Update message status (sent, delivered, read)
 */
export const updateMessageStatus = (
  messages: IMessage[],
  messageId: string,
  status: "sent" | "delivered" | "read"
): IMessage[] => {
  return messages.map(message => {
    if (message._id === messageId) {
      return {
        ...message,
        pending: false,
        sent: status === 'sent' || status === 'delivered' || status === 'read',
        received: status === 'delivered' || status === 'read',
      };
    }
    return message;
  });
};

/**
 * Add reaction to message
 */
export const addReactionToMessage = (
  messages: IMessage[],
  messageId: string,
  reaction: { id: string; user_id: string; emoji: string; created_at: string }
): IMessage[] => {
  return messages.map(message => {
    if (message._id === messageId) {
      const currentReactions = (message as any).reactions || [];
      const updatedReactions = [...currentReactions, reaction];
      
      return {
        ...message,
        reactions: updatedReactions,
      } as any;
    }
    return message;
  });
};

/**
 * Remove reaction from message
 */
export const removeReactionFromMessage = (
  messages: IMessage[],
  messageId: string,
  reactionId: string
): IMessage[] => {
  return messages.map(message => {
    if (message._id === messageId) {
      const currentReactions = (message as any).reactions || [];
      const updatedReactions = currentReactions.filter((r: any) => r.id !== reactionId);
      
      return {
        ...message,
        reactions: updatedReactions,
      } as any;
    }
    return message;
  });
};

/**
 * Get user profiles map from messages
 */
export const extractUserProfiles = (messages: AppMessage[]): Record<string, AppUser> => {
  const profiles: Record<string, AppUser> = {};
  
  messages.forEach(message => {
    // Add sender profile if not already present
    if (!profiles[message.sender_id]) {
      profiles[message.sender_id] = {
        id: message.sender_id,
        username: 'Unknown User', // This should be populated from actual user data
      };
    }
    
    // Add receiver profile if not already present
    if (!profiles[message.receiver_id]) {
      profiles[message.receiver_id] = {
        id: message.receiver_id,
        username: 'Unknown User', // This should be populated from actual user data
      };
    }
  });
  
  return profiles;
};

/**
 * Check if message is from current user
 */
export const isOwnMessage = (message: IMessage, currentUserId: string): boolean => {
  return message.user._id.toString() === currentUserId;
};

/**
 * Format message for reply preview
 */
export const formatReplyPreview = (message: IMessage): string => {
  const messageType = (message as any).messageType;
  
  if (messageType === 'shared_post') {
    return '📷 Shared a post';
  }
  if (messageType === 'shared_reel') {
    return '🎥 Shared a reel';
  }
  
  const text = message.text || '';
  return text.length > 50 ? `${text.substring(0, 50)}...` : text;
};
