import React from 'react';
import { View } from 'react-native';

interface SwipeableMessageProps {
  children: React.ReactNode;
  onSwipeLeft: () => void;
  onSwipeRight: () => void;
  enabled?: boolean;
  isOwnMessage: boolean; // Determines swipe direction
}

const SwipeableMessage: React.FC<SwipeableMessageProps> = ({
  children,
}) => {
  // Temporarily disable swipe functionality to fix the gesture handler issue
  // TODO: Implement with new gesture handler API later

  return (
    <View>
      {children}
    </View>
  );
};

export default SwipeableMessage;
