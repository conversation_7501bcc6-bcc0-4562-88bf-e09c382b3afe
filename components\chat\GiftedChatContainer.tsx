import React, { useState, useCallback, useEffect } from 'react';
import { View, StyleSheet, Alert, KeyboardAvoidingView, Platform } from 'react-native';
import { GiftedChat, IMessage, User } from 'react-native-gifted-chat';
import { useTheme } from '@/src/context/ThemeContext';
import { useDispatch, useSelector } from 'react-redux';
import { useQueryClient } from '@tanstack/react-query';

// Custom components
import GiftedChatBubble from './GiftedChatBubble';
import GiftedChatDay from './GiftedChatDay';
import GiftedChatInputToolbar from './GiftedChatInputToolbar';
import GiftedChatComposer from './GiftedChatComposer';
import GiftedChatSend from './GiftedChatSend';
import GiftedChatMessageWrapper from './GiftedChatMessageWrapper';
import EmojiReactionPicker from './EmojiReactionPicker';

// Utilities and hooks
import {
  convertToGiftedChatMessage,
  convertFromGiftedChatMessage,
  createOptimisticMessage,
  createOptimisticReplyMessage,
  updateMessageStatus,
  addReactionToMessage,
  removeReactionFromMessage,
  AppMessage,
  AppUser,
} from '@/utils/giftedChatAdapter';
import { useSocketChat } from '@/hooks/useSocketChat';
import {
  useChatMessages,
  useSendMessage,
  useSendReply,
  useReactionMutation,
} from '@/lib/query/hooks/useChatQuery';
import { queryKeys } from '@/lib/query/queryKeys';
import {
  selectSelectedMessage,
  clearMessageSelection,
  addOptimisticReaction,
  removeOptimisticReaction,
} from '@/src/store/slices/chatUISlice';

interface GiftedChatContainerProps {
  userId: string;
  recipientId: string;
  userProfiles: Record<string, AppUser>;
}

const GiftedChatContainer: React.FC<GiftedChatContainerProps> = ({
  userId,
  recipientId,
  userProfiles,
}) => {
  const { colors, isDarkMode } = useTheme();
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  
  // Local state
  const [messages, setMessages] = useState<IMessage[]>([]);
  const [replyToMessage, setReplyToMessage] = useState<AppMessage | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  
  // Redux selectors
  const selectedMessage = useSelector(selectSelectedMessage);
  

  
  // Generate chat ID for real-time subscription
  const chatId = userId && recipientId ? [userId, recipientId].sort().join("-") : "";
  
  // TanStack Query hooks
  const {
    data: messagesData,
    isLoading: messagesLoading,
    fetchNextPage,
    hasNextPage,
  } = useChatMessages(userId, recipientId);
  
  const sendMessageMutation = useSendMessage();
  const sendReplyMutation = useSendReply();
  const reactionMutation = useReactionMutation();
  
  // Socket.IO real-time chat
  const {
    isConnected,
    sendMessage: sendSocketMessage,
    sendTypingStatus,
  } = useSocketChat({
    userId: userId || "",
    chatId,
    onNewMessage: (message: any) => {
      // Type guard to ensure message has required properties
      if (!message?.id || !message?.content) {
        console.warn('Invalid message received:', message);
        return;
      }

      console.log('🔥 Socket.IO: New message received!', message.id);

      // Convert to AppMessage format
      const appMessage: AppMessage = {
        id: message.id,
        content: message.content,
        sender_id: message.sender_id,
        receiver_id: message.receiver_id,
        created_at: message.created_at,
        is_read: message.is_read || false,
        status: message.status || 'sent',
        message_type: message.message_type || 'text',
        reply_to_message_id: message.reply_to_message_id,
      };

      // Convert to GiftedChat format and add to messages
      const giftedMessage = convertToGiftedChatMessage(appMessage, userId, userProfiles);
      setMessages(previousMessages => GiftedChat.append(previousMessages, [giftedMessage]));

      // Update TanStack Query cache optimistically
      queryClient.setQueryData(
        queryKeys.messages.messages(recipientId),
        (oldData: any) => {
          if (!oldData?.pages) return oldData;

          // Add message to the first page (most recent)
          const newPages = [...oldData.pages];
          if (newPages[0]) {
            newPages[0] = {
              ...newPages[0],
              messages: [appMessage, ...newPages[0].messages]
            };
          }

          return {
            ...oldData,
            pages: newPages
          };
        }
      );

      // Also invalidate conversations to update last message
      queryClient.invalidateQueries({
        queryKey: queryKeys.messages.conversations()
      });
    },
    onTypingUpdate: (data) => {
      if (data.userId !== userId) {
        setIsTyping(data.isTyping);
      }
    },
    onMessageStatusUpdate: (data) => {
      setMessages(prevMessages =>
        updateMessageStatus(prevMessages, data.messageId, data.status as "sent" | "delivered" | "read")
      );
    },
    onNewReaction: (data) => {
      setMessages(prevMessages =>
        addReactionToMessage(prevMessages, data.messageId, data.reaction)
      );
    },
    onReactionRemoved: (data) => {
      setMessages(prevMessages => 
        removeReactionFromMessage(prevMessages, data.messageId, data.reactionId)
      );
    },
  });
  
  // Convert app messages to GiftedChat format when data changes
  useEffect(() => {
    if (messagesData?.pages) {
      const appMessages = messagesData.pages.flatMap(page => page.messages) || [];
      const giftedMessages = appMessages
        .filter(msg => msg && typeof msg === 'object' && 'id' in msg)
        .map(msg => convertToGiftedChatMessage(msg as AppMessage, userId, userProfiles));

      // Sort messages by creation date (newest first for GiftedChat)
      const sortedMessages = giftedMessages.sort((a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );

      setMessages(sortedMessages);

      console.log('📱 GiftedChat: Messages updated from cache', {
        totalMessages: sortedMessages.length,
        pages: messagesData.pages.length
      });
    }
  }, [messagesData, userId, userProfiles]);

  // Sync with cache on mount and when chat changes
  useEffect(() => {
    console.log('🔄 GiftedChat: Syncing with TanStack Query cache for chat:', recipientId);

    // Prefetch messages if not already cached
    if (!messagesData?.pages?.length) {
      queryClient.prefetchInfiniteQuery({
        queryKey: queryKeys.messages.messages(recipientId),
        queryFn: ({ pageParam = 0 }) => {
          // This will be handled by the useChatMessages hook
          return Promise.resolve({ messages: [], nextCursor: null });
        },
        initialPageParam: 0,
      });
    }
  }, [recipientId, queryClient, messagesData]);

  // Handle sending messages
  const onSend = useCallback(async (newMessages: IMessage[] = []) => {
    const message = newMessages[0];
    if (!message || !message.text?.trim()) return;

    // Create optimistic message with reply info if replying
    let optimisticMessage: IMessage;
    if (replyToMessage) {
      optimisticMessage = createOptimisticReplyMessage(
        message.text,
        userId,
        recipientId,
        replyToMessage,
        userProfiles
      );
    } else {
      optimisticMessage = createOptimisticMessage(
        message.text,
        userId,
        recipientId,
        userProfiles
      );
    }

    try {
      // Add optimistic message immediately
      setMessages(previousMessages => GiftedChat.append(previousMessages, [optimisticMessage]));

      // Convert to app message format
      const appMessage = convertFromGiftedChatMessage(message, recipientId);

      // Send via Socket.IO for real-time delivery
      if (sendSocketMessage && isConnected) {
        const socketMessage = sendSocketMessage({
          ...appMessage,
          sender_id: userId,
          receiver_id: recipientId,
          content: message.text,
          is_read: false,
          reply_to_message_id: replyToMessage?.id,
        });

        // Update optimistic message with real ID if socket message has ID
        if (socketMessage?.id) {
          setMessages(prevMessages =>
            prevMessages.map(msg =>
              msg._id === optimisticMessage._id
                ? { ...msg, _id: socketMessage.id, pending: false, sent: true }
                : msg
            )
          );
        }
      }

      // Send via TanStack Query mutation for database persistence
      let dbMessage;
      if (replyToMessage) {
        dbMessage = await sendReplyMutation.mutateAsync({
          senderId: userId,
          receiverId: recipientId,
          content: message.text,
          replyToMessageId: replyToMessage.id,
          messageType: 'text'
        });
        setReplyToMessage(null);
      } else {
        dbMessage = await sendMessageMutation.mutateAsync({
          senderId: userId,
          receiverId: recipientId,
          content: message.text,
          messageType: 'text'
        });
      }

      // Update optimistic message with database response
      if (dbMessage && (dbMessage as any)?.id) {
        setMessages(prevMessages =>
          prevMessages.map(msg =>
            msg._id === optimisticMessage._id
              ? { ...msg, _id: (dbMessage as any).id, pending: false, sent: true }
              : msg
          )
        );
      }

      // Stop typing indicator
      if (sendTypingStatus) {
        sendTypingStatus(false);
      }

    } catch (error) {
      console.error('Failed to send message:', error);
      Alert.alert('Error', 'Failed to send message. Please try again.');

      // Remove optimistic message on error
      setMessages(prevMessages =>
        prevMessages.filter(msg => msg._id !== optimisticMessage._id)
      );
    }
  }, [userId, recipientId, replyToMessage, sendSocketMessage, isConnected, sendMessageMutation, sendReplyMutation, sendTypingStatus]);
  
  // Handle typing
  const handleTyping = useCallback((isTyping: boolean) => {
    if (sendTypingStatus) {
      sendTypingStatus(isTyping);
    }
  }, [sendTypingStatus]);
  
  // Handle swipe to reply
  const handleSwipeReply = useCallback((message: IMessage) => {
    const appMessage = (message as any).appMessage;
    if (appMessage) {
      setReplyToMessage(appMessage);

      // Note: GiftedChat doesn't expose a ref for focusing input
      // The input will automatically focus when reply is set
    }
  }, []);
  
  // Handle long press for reactions
  const handleLongPress = useCallback((_message: IMessage) => {
    // This will be handled by the message wrapper component
    // which will dispatch Redux actions for emoji picker
  }, []);
  
  // Handle emoji reaction
  const handleEmojiReaction = useCallback(async (emoji: string) => {
    if (!selectedMessage.messageId) return;
    
    try {
      // Add optimistic reaction
      dispatch(addOptimisticReaction({
        messageId: selectedMessage.messageId,
        emoji
      }));
      
      // Send reaction via mutation
      await reactionMutation.mutateAsync({
        messageId: selectedMessage.messageId,
        emoji,
        userId
      });
      
      // Clear selection
      dispatch(clearMessageSelection());
      
    } catch (error) {
      console.error('Failed to add reaction:', error);
      dispatch(removeOptimisticReaction(selectedMessage.messageId));
    }
  }, [selectedMessage.messageId, userId, dispatch, reactionMutation]);
  
  // Handle load earlier messages
  const onLoadEarlier = useCallback(() => {
    if (hasNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, fetchNextPage]);
  
  // Create current user object
  const currentUser: User = {
    _id: userId,
    name: userProfiles[userId]?.username || 'You',
    avatar: userProfiles[userId]?.avatar_url,
  };
  
  return (
    <View style={styles.container}>
      <View style={[styles.chatContainer, { backgroundColor: colors.background }]}>
        <GiftedChat
          messages={messages}
          onSend={onSend}
          user={currentUser}
          isTyping={isTyping}
          loadEarlier={hasNextPage}
          onLoadEarlier={onLoadEarlier}
          isLoadingEarlier={messagesLoading}
          renderBubble={(props) => (
            <GiftedChatBubble
              {...props}
              currentUserId={userId}
              onLongPress={handleLongPress}
              onReplyPress={handleSwipeReply}
            />
          )}
          renderDay={(props) => <GiftedChatDay {...props} />}
          renderInputToolbar={(props) => (
            <GiftedChatInputToolbar
              {...props}
              replyToMessage={replyToMessage}
              onCancelReply={() => setReplyToMessage(null)}
              currentUserId={userId}
            />
          )}
          renderComposer={(props) => (
            <GiftedChatComposer
              {...props}
              onTyping={handleTyping}
            />
          )}
          renderSend={(props) => (
            <GiftedChatSend
              {...props}
              disabled={!props.text?.trim()}
            />
          )}
          renderMessage={(props) => (
            <GiftedChatMessageWrapper
              message={props.currentMessage!}
              currentUserId={userId}
              onSwipeReply={handleSwipeReply}
              onLongPress={handleLongPress}
            >
              {/* Render the bubble component */}
              <GiftedChatBubble {...props} currentUserId={userId} />
            </GiftedChatMessageWrapper>
          )}
          showUserAvatar={false}
          showAvatarForEveryMessage={false}
          renderUsernameOnMessage={false}
          inverted={true}
          infiniteScroll={true}
          keyboardShouldPersistTaps="never"
          bottomOffset={0}
          minInputToolbarHeight={60}
          maxInputLength={1000}
        />
        
        {/* Emoji Reaction Picker */}
        <EmojiReactionPicker
          visible={selectedMessage.isVisible}
          messagePosition={selectedMessage.position}
          onEmojiSelect={handleEmojiReaction}
          onClose={() => dispatch(clearMessageSelection())}
        />
      </View>

      {/* KeyboardAvoidingView for Android only, as per GiftedChat docs */}
      {Platform.OS === 'android' && <KeyboardAvoidingView behavior="padding" />}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  chatContainer: {
    flex: 1,
  },
});

export default GiftedChatContainer;
