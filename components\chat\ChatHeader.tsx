
import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import { Feather } from '@expo/vector-icons';

import { useTheme } from '@/src/context/ThemeContext';
import CachedImage from '@/components/CachedImage';
import { AppUser } from '@/utils/giftedChatAdapter';

interface ChatHeaderProps {
  recipientProfile?: AppUser;
  isLoading: boolean;
  onBackPress: () => void;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
  recipientProfile,
  isLoading,
  onBackPress
}) => {
  const { colors } = useTheme();

  return (
    <View style={[styles.header, {
      backgroundColor: colors.background,
      borderBottomColor: colors.cardBorder
    }]}>
      {/* Back Button */}
      <TouchableOpacity
        onPress={onBackPress}
        style={styles.backButton}
        activeOpacity={0.7}
      >
        <Feather name="arrow-left" size={24} color={colors.text} />
      </TouchableOpacity>

      {/* Header Content */}
      <View style={styles.headerContent}>
        {isLoading ? (
          <View style={styles.headerLoading}>
            <ActivityIndicator size="small" color={colors.primary} />
            <Text style={[styles.headerTitle, { color: colors.text }]}>
              Loading...
            </Text>
          </View>
        ) : recipientProfile ? (
          <View style={styles.headerInfo}>
            <CachedImage
              uri={recipientProfile.avatar_url || ''}
              style={[styles.headerAvatar, { borderColor: colors.cardBorder }]}
              fallbackUri="https://via.placeholder.com/40"
              showLoader={true}
              loaderColor={colors.primary}
            />
            <View style={styles.headerTextContainer}>
              <Text
                style={[styles.headerTitle, { color: colors.text }]}
                numberOfLines={1}
                className="font-rubik-bold"
              >
                {recipientProfile.username}
              </Text>
              <Text
                style={[styles.headerSubtitle, { color: colors.textSecondary }]}
                className="font-rubik-regular"
              >
                Active now
              </Text>
            </View>
          </View>
        ) : (
          <Text
            style={[styles.headerTitle, { color: colors.text }]}
            className="font-rubik-bold"
          >
            Chat
          </Text>
        )}
      </View>

      {/* Header Actions */}
      <View style={styles.headerActions}>
        <TouchableOpacity
          style={styles.actionButton}
          activeOpacity={0.7}
        >
          <Feather name="phone" size={20} color={colors.textSecondary} />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.actionButton}
          activeOpacity={0.7}
        >
          <Feather name="video" size={20} color={colors.textSecondary} />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.actionButton}
          activeOpacity={0.7}
        >
          <Feather name="more-vertical" size={20} color={colors.textSecondary} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default ChatHeader;

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    minHeight: 64,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
    borderRadius: 20,
  },
  headerContent: {
    flex: 1,
    marginLeft: 4,
  },
  headerLoading: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  headerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
    borderWidth: 1,
  },
  headerTextContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Rubik-Bold',
    lineHeight: 22,
  },
  headerSubtitle: {
    fontSize: 13,
    fontFamily: 'Rubik-Regular',
    marginTop: 2,
    lineHeight: 16,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  actionButton: {
    padding: 8,
    borderRadius: 20,
    width: 36,
    height: 36,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
