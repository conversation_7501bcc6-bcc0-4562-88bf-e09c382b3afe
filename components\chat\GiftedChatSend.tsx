import React from 'react';
import { TouchableOpacity, StyleSheet } from 'react-native';
import { Send, SendProps, IMessage } from 'react-native-gifted-chat';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '@/src/context/ThemeContext';

interface CustomSendProps extends SendProps<IMessage> {
  disabled?: boolean;
}

const GiftedChatSend: React.FC<CustomSendProps> = ({
  disabled = false,
  ...props
}) => {
  const { colors, isDarkMode } = useTheme();

  return (
    <Send
      {...props}
      disabled={disabled}
      containerStyle={[
        styles.container,
        {
          backgroundColor: disabled 
            ? 'transparent' 
            : isDarkMode 
              ? 'rgba(128, 128, 128, 0.2)' 
              : 'rgba(128, 128, 128, 0.1)',
          borderColor: disabled 
            ? 'transparent' 
            : isDarkMode 
              ? 'rgba(128, 128, 128, 0.4)' 
              : 'rgba(128, 128, 128, 0.3)',
        }
      ]}
    >
      <TouchableOpacity
        style={[
          styles.sendButton,
          {
            opacity: disabled ? 0.5 : 1,
          }
        ]}
        disabled={disabled}
        activeOpacity={0.7}
      >
        <Feather 
          name="send" 
          size={20} 
          color={disabled 
            ? colors.textTertiary 
            : isDarkMode 
              ? '#808080' 
              : '#606060'
          } 
        />
      </TouchableOpacity>
    </Send>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    marginLeft: 8,
    borderRadius: 20,
    borderWidth: 1,
    width: 40,
    height: 40,
  },
  sendButton: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: '100%',
  },
});

export default GiftedChatSend;
