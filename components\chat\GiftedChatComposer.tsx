import React from 'react';
import { StyleSheet } from 'react-native';
import { Composer, ComposerProps, IMessage } from 'react-native-gifted-chat';
import { useTheme } from '@/src/context/ThemeContext';

interface CustomComposerProps extends ComposerProps {
  onTyping?: (isTyping: boolean) => void;
}

const GiftedChatComposer: React.FC<CustomComposerProps> = ({
  onTyping,
  ...props
}) => {
  const { colors } = useTheme();

  const handleTextChanged = (text: string) => {
    // Call original onTextChanged
    if (props.onTextChanged) {
      props.onTextChanged(text);
    }

    // Handle typing indicator
    if (onTyping) {
      onTyping(text.length > 0);
    }
  };

  return (
    <Composer
      {...props}
      onTextChanged={handleTextChanged}
      textInputStyle={[
        styles.textInput,
        {
          color: colors.text,
          backgroundColor: 'transparent',
        }
      ]}
      placeholderTextColor={colors.textTertiary}
      placeholder="Type a message..."
      multiline={true}
      textInputProps={{
        ...props.textInputProps,
        maxLength: 1000, // Reasonable message length limit
        autoCorrect: true,
        autoCapitalize: 'sentences',
        keyboardType: 'default',
        returnKeyType: 'default',
        blurOnSubmit: false,
      }}
    />
  );
};

const styles = StyleSheet.create({
  textInput: {
    fontSize: 16,
    fontFamily: 'Rubik-Regular',
    lineHeight: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    maxHeight: 100, // Limit height for multiline
    minHeight: 40,
  },
});

export default GiftedChatComposer;
