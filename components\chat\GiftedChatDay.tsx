import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { IMessage } from 'react-native-gifted-chat';
import { useTheme } from '@/src/context/ThemeContext';
import { formatChatDate } from '@/utils/dateUtils';

interface CustomDayProps {
  createdAt: number | Date;
  currentMessage?: IMessage;
  dateFormat?: string;
  dateFormatCalendar?: object;
  containerStyle?: any;
  wrapperStyle?: any;
  textStyle?: any;
}

const GiftedChatDay: React.FC<CustomDayProps> = ({
  createdAt,
  currentMessage
}) => {
  const { colors, isDarkMode } = useTheme();

  // Use createdAt if currentMessage is not available
  const messageDate = currentMessage ? new Date(currentMessage.createdAt) : new Date(createdAt);
  const formattedDate = formatChatDate(messageDate.toISOString());

  return (
    <View style={styles.container}>
      <View style={[
        styles.dayContainer,
        {
          backgroundColor: isDarkMode
            ? 'rgba(255, 255, 255, 0.08)'
            : 'rgba(0, 0, 0, 0.03)',
          borderColor: isDarkMode
            ? 'rgba(255, 255, 255, 0.1)'
            : 'rgba(0, 0, 0, 0.05)',
        }
      ]}>
        <Text style={[
          styles.dayText,
          { color: colors.textSecondary }
        ]}>
          {formattedDate}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginVertical: 12,
    paddingHorizontal: 16,
  },
  dayContainer: {
    paddingHorizontal: 14,
    paddingVertical: 7,
    borderRadius: 15,
    borderWidth: 0.5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  dayText: {
    fontSize: 11,
    fontFamily: 'Rubik-Medium',
    textAlign: 'center',
    letterSpacing: 0.3,
  },
});

export default GiftedChatDay;
