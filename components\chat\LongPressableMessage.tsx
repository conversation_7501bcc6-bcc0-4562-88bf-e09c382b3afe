import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import { useDispatch } from 'react-redux';
import { selectMessageForReaction } from '@/src/store/slices/chatUISlice';

interface LongPressableMessageProps {
  children: React.ReactNode;
  onLongPress?: (event: any) => void;
  enabled?: boolean;
  messageId: string;
}

const LongPressableMessage: React.FC<LongPressableMessageProps> = ({
  children,
  onLongPress,
  enabled = true,
  messageId,
}) => {
  const dispatch = useDispatch();

  const handleLongPress = () => {
    if (!enabled) return;

    // Dispatch action for message selection
    dispatch(selectMessageForReaction({
      messageId,
      position: { x: 0, y: 0, width: 0, height: 0 } // Simplified for now
    }));

    if (onLongPress) {
      onLongPress({});
    }
  };

  if (!enabled) {
    return <View>{children}</View>;
  }

  return (
    <TouchableOpacity
      onLongPress={handleLongPress}
      delayLongPress={500}
      activeOpacity={0.95}
    >
      {children}
    </TouchableOpacity>
  );
};

export default LongPressableMessage;
