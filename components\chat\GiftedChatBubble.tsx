import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Bubble, BubbleProps, IMessage } from 'react-native-gifted-chat';
import { useTheme } from '@/src/context/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import ReactionBadge from './ReactionBadge';

interface CustomBubbleProps extends BubbleProps<IMessage> {
  onLongPress?: (message: IMessage) => void;
  onReplyPress?: (message: IMessage) => void;
  currentUserId: string;
}

const GiftedChatBubble: React.FC<CustomBubbleProps> = ({
  currentMessage,
  user,
  onLongPress,
  onReplyPress,
  currentUserId,
  ...props
}) => {
  const { colors, isDarkMode } = useTheme();

  if (!currentMessage) return null;

  const isOwnMessage = currentMessage.user._id.toString() === currentUserId;
  const reactions = (currentMessage as any).reactions || [];
  const replyToMessage = (currentMessage as any).replyToMessage;

  // WhatsApp-style bubble colors
  const bubbleColor = isOwnMessage
    ? "#128C7E" // WhatsApp green for sender
    : isDarkMode
      ? "#2A2A2A" // Dark gray for receiver in dark mode
      : "#FFFFFF"; // White for receiver in light mode

  const textColor = isOwnMessage
    ? "#FFFFFF" // White text on green background
    : colors.text; // Theme text color for receiver

  const timeColor = isOwnMessage
    ? "rgba(255, 255, 255, 0.7)" // Semi-transparent white on green
    : colors.textTertiary;

  const handleLongPress = () => {
    if (onLongPress && currentMessage) {
      onLongPress(currentMessage);
    }
  };

  const handleReplyPress = () => {
    if (onReplyPress && currentMessage) {
      onReplyPress(currentMessage);
    }
  };

  return (
    <View style={[
      styles.container,
      isOwnMessage ? styles.containerRight : styles.containerLeft
    ]}>
      {/* Reply indicator - WhatsApp style */}
      {replyToMessage && (
        <View style={[
          styles.replyContainer,
          {
            backgroundColor: isOwnMessage
              ? "rgba(255, 255, 255, 0.15)"
              : isDarkMode
                ? "rgba(255, 255, 255, 0.08)"
                : "rgba(0, 0, 0, 0.03)",
            borderLeftColor: isOwnMessage ? "#FFFFFF" : "#128C7E",
          }
        ]}>
          <Text style={[
            styles.replyAuthor,
            { color: isOwnMessage ? "#FFFFFF" : "#128C7E" }
          ]}>
            {replyToMessage.sender_id === currentUserId ? 'You' : 'Other User'}
          </Text>
          <Text style={[
            styles.replyText,
            { color: isOwnMessage ? "rgba(255, 255, 255, 0.9)" : colors.textSecondary }
          ]} numberOfLines={1}>
            {replyToMessage.content || 'Message'}
          </Text>
        </View>
      )}

      {/* Main message bubble */}
      <TouchableOpacity
        onLongPress={handleLongPress}
        activeOpacity={0.8}
        style={[
          styles.bubble,
          {
            backgroundColor: bubbleColor,
            borderBottomRightRadius: isOwnMessage ? 4 : 18, // WhatsApp tail effect
            borderBottomLeftRadius: isOwnMessage ? 18 : 4,
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.1,
            shadowRadius: 2,
            elevation: 2,
          }
        ]}
      >
        {/* Message text */}
        <Text style={[
          styles.messageText,
          { color: textColor }
        ]}>
          {currentMessage.text}
        </Text>

        {/* Message info (time and status) */}
        <View style={styles.messageInfo}>
          <Text style={[
            styles.timeText,
            { color: timeColor }
          ]}>
            {new Date(currentMessage.createdAt).toLocaleTimeString('en-US', {
              hour: 'numeric',
              minute: '2-digit',
              hour12: true,
            })}
          </Text>
          
          {/* Message status indicators for own messages */}
          {isOwnMessage && (
            <View style={styles.statusContainer}>
              {currentMessage.pending && (
                <Ionicons name="time-outline" size={12} color={timeColor} />
              )}
              {currentMessage.sent && !currentMessage.received && (
                <Ionicons name="checkmark" size={12} color={timeColor} />
              )}
              {currentMessage.received && (
                <Ionicons name="checkmark-done" size={12} color={timeColor} />
              )}
            </View>
          )}
        </View>
      </TouchableOpacity>

      {/* Reactions */}
      {reactions.length > 0 && (
        <View style={[
          styles.reactionsContainer,
          isOwnMessage ? styles.reactionsRight : styles.reactionsLeft
        ]}>
          <ReactionBadge
            reactions={reactions}
            currentUserId={currentUserId}
            onReactionPress={(emoji: string) => {
              // Handle reaction press (remove if it's user's reaction)
              console.log('Reaction pressed:', emoji);
            }}
          />
        </View>
      )}

      {/* Reply button (shown on swipe or long press) */}
      {onReplyPress && (
        <TouchableOpacity
          style={[
            styles.replyButton,
            isOwnMessage ? styles.replyButtonRight : styles.replyButtonLeft
          ]}
          onPress={handleReplyPress}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons 
            name="return-up-forward" 
            size={16} 
            color={colors.textSecondary} 
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 2,
    marginHorizontal: 8,
    maxWidth: '80%',
  },
  containerLeft: {
    alignSelf: 'flex-start',
  },
  containerRight: {
    alignSelf: 'flex-end',
  },
  replyContainer: {
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderLeftWidth: 3,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    marginBottom: 2,
  },
  replyAuthor: {
    fontSize: 12,
    fontFamily: 'Rubik-Medium',
    marginBottom: 2,
  },
  replyText: {
    fontSize: 13,
    fontFamily: 'Rubik-Regular',
    opacity: 0.8,
  },
  bubble: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 18,
    minWidth: 80,
  },
  messageText: {
    fontSize: 16,
    fontFamily: 'Rubik-Regular',
    lineHeight: 22,
    marginBottom: 4,
  },
  messageInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: 2,
  },
  timeText: {
    fontSize: 11,
    fontFamily: 'Rubik-Regular',
    marginRight: 4,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  reactionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 4,
    marginHorizontal: 4,
  },
  reactionsLeft: {
    justifyContent: 'flex-start',
  },
  reactionsRight: {
    justifyContent: 'flex-end',
  },
  replyButton: {
    position: 'absolute',
    top: '50%',
    transform: [{ translateY: -12 }],
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 12,
    padding: 4,
    opacity: 0, // Hidden by default, shown on interaction
  },
  replyButtonLeft: {
    right: -30,
  },
  replyButtonRight: {
    left: -30,
  },
});

export default GiftedChatBubble;
