import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  StyleSheet,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, router } from 'expo-router';
import { Feather } from '@expo/vector-icons';

import { useTheme } from '@/src/context/ThemeContext';
import { supabase } from '@/lib/supabase';
import { useChatUserProfile } from '@/lib/query/hooks/useChatQuery';
import GiftedChatContainer from '@/components/chat/GiftedChatContainer';
import ChatHeader from '@/components/chat/ChatHeader';
import { AppUser } from '@/utils/giftedChatAdapter';

const ChatScreenContent = () => {
  const { id: recipientId } = useLocalSearchParams();
  const recipientIdString = Array.isArray(recipientId) ? recipientId[0] : recipientId;
  const [userId, setUserId] = useState<string | null>(null);
  const { colors } = useTheme();

  // Get current user ID
  useEffect(() => {
    const getCurrentUser = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          setUserId(user.id);
        }
      } catch (error) {
        console.error('Error getting current user:', error);
      }
    };

    getCurrentUser();
  }, []);

  // Get recipient profile data
  const { data: recipientData, isLoading: profileLoading, error: profileError } = useChatUserProfile(recipientIdString || '');

  // Handle back navigation
  const handleBackPress = () => {
    router.back();
  };

  // Create user profiles object for GiftedChat
  const userProfiles: Record<string, AppUser> = {};
  if (userId) {
    userProfiles[userId] = {
      id: userId,
      username: 'You',
      avatar_url: '',
    };
  }
  if (recipientData) {
    userProfiles[recipientData.id] = recipientData;
  }

  // Show loading state
  if (profileLoading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <ChatHeader
          recipientProfile={undefined}
          isLoading={true}
          onBackPress={handleBackPress}
        />
        <View style={styles.loadingContainer}>
          <Feather
            name="message-circle"
            size={48}
            color={colors.textSecondary}
            style={styles.emptyStateIcon}
          />
          <ActivityIndicator size="large" color={colors.primary} />
          <Text
            style={[styles.loadingText, { color: colors.textSecondary }]}
            className="font-rubik-medium"
          >
            Loading chat...
          </Text>
          <Text
            style={[styles.emptyStateSubtext, { color: colors.textSecondary }]}
            className="font-rubik-regular"
          >
            Please wait while we connect you
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // Show error state
  if (profileError) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <ChatHeader
          recipientProfile={undefined}
          isLoading={false}
          onBackPress={handleBackPress}
        />
        <View style={styles.errorContainer}>
          <Feather
            name="alert-circle"
            size={64}
            color={colors.textSecondary}
            style={styles.emptyStateIcon}
          />
          <Text
            style={[styles.errorText, { color: colors.text }]}
            className="font-rubik-bold"
          >
            Failed to load chat
          </Text>
          <Text
            style={[styles.emptyStateSubtext, { color: colors.textSecondary }]}
            className="font-rubik-regular"
          >
            Something went wrong. Please try again.
          </Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={() => {
              // Retry by refetching the profile data
              Alert.alert('Retry', 'Please go back and try again');
            }}
            activeOpacity={0.8}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // Show empty state if no user ID or recipient ID
  if (!userId || !recipientIdString) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <ChatHeader
          recipientProfile={recipientData}
          isLoading={false}
          onBackPress={handleBackPress}
        />
        <View style={styles.emptyStateContainer}>
          <Feather
            name="message-circle"
            size={64}
            color={colors.textSecondary}
            style={styles.emptyStateIcon}
          />
          <Text style={[styles.emptyStateText, { color: colors.text }]}>
            Unable to load chat
          </Text>
          <Text style={[styles.emptyStateSubtext, { color: colors.textSecondary }]}>
            Please try again later
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // Main chat interface
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ChatHeader
        recipientProfile={recipientData}
        isLoading={false}
        onBackPress={handleBackPress}
      />

      <View style={styles.chatContainer}>
        <GiftedChatContainer
          userId={userId}
          recipientId={recipientIdString}
          userProfiles={userProfiles}
        />
      </View>
    </SafeAreaView>
  );
};

export default ChatScreenContent;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontFamily: 'Rubik-Medium',
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  errorText: {
    fontSize: 16,
    fontFamily: 'Rubik-Medium',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Rubik-Bold',
    textAlign: 'center',
  },
  chatContainer: {
    flex: 1,
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyStateIcon: {
    marginBottom: 16,
    opacity: 0.5,
  },
  emptyStateText: {
    fontSize: 16,
    fontFamily: 'Rubik-Medium',
    textAlign: 'center',
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    fontFamily: 'Rubik-Regular',
    textAlign: 'center',
    opacity: 0.7,
  },
});