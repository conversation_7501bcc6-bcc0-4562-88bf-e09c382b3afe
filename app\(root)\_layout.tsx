import { Stack } from "expo-router";

const Layout = () => {
  return (
    <Stack>
      <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
      <Stack.Screen name="create-profile" options={{ headerShown: false }} />
      <Stack.Screen
        name="posts-comments-screen"
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="reels-comments-screen"
        options={{ headerShown: false }}
      />
      <Stack.Screen name="edit-profile" options={{ headerShown: false }} />
      <Stack.Screen name="notifications" options={{ headerShown: false }} />
      <Stack.Screen name="post/[id]" options={{ headerShown: false }} />
      <Stack.Screen name="post-likes/[id]" options={{ headerShown: false }} />
      <Stack.Screen name="reel/[id]" options={{ headerShown: false }} />
      <Stack.Screen name="rooms/[id]" options={{ headerShown: false }} />
      <Stack.Screen name="userProfile/[id]" options={{ headerShown: false }} />
      <Stack.Screen name="followers/[id]" options={{ headerShown: false }} />
      <Stack.Screen name="following/[id]" options={{ headerShown: false }} />
      <Stack.Screen name="chat/index" options={{ headerShown: false }} />
      <Stack.Screen name="demo/index" options={{ headerShown: false }} />
      <Stack.Screen name="demos" options={{ headerShown: false }} />
      <Stack.Screen name="chats/[id]" options={{ headerShown: false }} />
      <Stack.Screen name="settings" options={{ headerShown: false }} />
      <Stack.Screen name="welcome-main" options={{ headerShown: false }} />
      <Stack.Screen name="appearance" options={{ headerShown: false }} />
      <Stack.Screen name="terms-and-conditions" options={{ headerShown: false }} />
      <Stack.Screen name="privacy-policy" options={{ headerShown: false }} />


    </Stack>
  );
};

export default Layout;
